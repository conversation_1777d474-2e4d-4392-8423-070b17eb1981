name: Continous Deployment

on:
  push:
    branches:
      - main

jobs:
  release-please:
    runs-on: ubuntu-latest
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0080882f6c36860b6ba35c610c98ce87d4e2f26f # v2.10.2
        with:
          egress-policy: audit
      -
        name: Create release
        id: release-please
        uses: googleapis/release-please-action@7987652d64b4581673a76e33ad5e98e3dd56832f # v4.1.3
        with:
          release-type: python
      -
        name: Dispatch Edge Build
        if: always()
        uses: peter-evans/repository-dispatch@ff45666b9427631e3450c54a1bcbee4d9ff4d7c0 # v3.0.0
        with:
          repository: shinybrar/skaha
          event-type: edge-build
          token: ${{ secrets.GITHUB_TOKEN }}
          client-payload: |-
            {
              "releases_created": "${{ steps.release-please.outputs.releases_created }}",
              "tag_name": "${{ steps.release-please.outputs.tag_name }}",
              "sha": "${{ steps.release-please.outputs.sha }}"
            }
      -
        name: Dispatch Release Build
        if: ${{ steps.release-please.outputs.release_created }}
        uses: peter-evans/repository-dispatch@ff45666b9427631e3450c54a1bcbee4d9ff4d7c0 # v3.0.0
        with:
          repository: shinybrar/skaha
          event-type: release-build
          token: ${{ secrets.GITHUB_TOKEN }}
          client-payload: |-
            {
              "releases_created": "${{ steps.release-please.outputs.releases_created }}",
              "tag_name": "${{ steps.release-please.outputs.tag_name }}",
              "sha": "${{ steps.release-please.outputs.sha }}"
            }
      -
        name: Dispatch PYPI Release
        if: ${{ steps.release-please.outputs.release_created }}
        uses: peter-evans/repository-dispatch@ff45666b9427631e3450c54a1bcbee4d9ff4d7c0 # v3.0.0
        with:
          repository: shinybrar/skaha
          event-type: pypi-release
          token: ${{ secrets.GITHUB_TOKEN }}
          client-payload: |-
            {
              "releases_created": "${{ steps.release-please.outputs.releases_created }}",
              "tag_name": "${{ steps.release-please.outputs.tag_name }}",
              "sha": "${{ steps.release-please.outputs.sha }}"
            }
      -
        name: Dispatch Docs Publish
        if: always()
        uses: peter-evans/repository-dispatch@ff45666b9427631e3450c54a1bcbee4d9ff4d7c0 # v3.0.0
        with:
          repository: shinybrar/skaha
          event-type: docs-publish
          token: ${{ secrets.GITHUB_TOKEN }}
          client-payload: |-
            {
              "releases_created": "${{ steps.release-please.outputs.releases_created }}",
              "tag_name": "${{ steps.release-please.outputs.tag_name }}",
              "sha": "${{ steps.release-please.outputs.sha }}"
            }
