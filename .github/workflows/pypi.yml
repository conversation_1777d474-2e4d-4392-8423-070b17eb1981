name: Publish Skaha to PyPI

on:
  repository_dispatch:
    types: [pypi-release]

permissions:
  contents: read

jobs:
  pypi-release:
    runs-on: ubuntu-latest
    permissions:
      id-token: write # Generate OIDC token for attestations
      attestations: write # Attest the build provenance
    environment:
      name: pypi-release
      url: https://pypi.org/p/skaha
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0080882f6c36860b6ba35c610c98ce87d4e2f26f # v2.10.2
        with:
          egress-policy: audit

      -
        name: Echo Build Dispatch
        run: |
          echo "Pypi Release Triggered"
          echo "Event Payload: ${{ toJson(github.event.client_payload) }}"
      -
        name: Checkout Code
        uses: actions/checkout@eef61447b9ff4aafe5dcd4e0bbf5d482be7e7871 # v4.2.1
        with:
          ref: ${{ github.event.client_payload.tag_name }}
      -
        name: Install uv
        uses: astral-sh/setup-uv@f3bcaebff5eace81a1c062af9f9011aae482ca9d # v3.1.7
        with:
          enable-cache: false
      -
        name: Set up Python
        run: uv python install 3.13
      -
        name: Build release distributions
        run: uv build
      -
        name: Publish release distributions to PyPI
        uses: pypa/gh-action-pypi-publish@67339c736fd9354cd4f8cb0b744f2b82a74b5c70 # v1.12.3
        with:
          attestations: false
          packages-dir: dist/
          verbose: true
          print-hash: true
      -
        name: Attestations for PYPI Wheel
        uses: actions/attest-build-provenance@ef244123eb79f2f7a7e75d99086184180e6d0018 # v1.4.4
        with:
          subject-path: |
            dist/*.whl
          subject-name: skaha-wheel
          show-summary: true
      -
        name: Attestations for PYPI Source
        uses: actions/attest-build-provenance@ef244123eb79f2f7a7e75d99086184180e6d0018 # v1.4.4
        with:
          subject-path: |
            dist/*.tar.gz
          subject-name: skaha-source
          show-summary: true
