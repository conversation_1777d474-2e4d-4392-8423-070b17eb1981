---
name: Bug Report
about: Create a report to help us improve
title: "[BUG] "
labels: bug
assignees: shinybrar

---

## Bug Description

<!-- Provide a clear and concise description of what the bug is -->

## Steps to Reproduce

<!-- List the exact steps to reproduce the behavior -->
1.
2.
3.

## Expected Behavior

<!-- Describe what you expected to happen -->

## System Information

<!--
REQUIRED: Please run `skaha version --debug` and paste the complete output below.
This information is crucial for debugging your issue.
-->

```
Paste the complete output of `skaha version --debug` here
```

## Error Messages and Logs

<!--
If applicable, include any error messages, stack traces, or relevant log output.
Use code blocks to format them properly.
-->

```
Paste error messages or logs here
```

## Screenshots

<!-- If applicable, add screenshots to help explain your problem -->

## Additional Context

<!--
Add any other context about the problem here, such as:
- When the issue started occurring
- Whether it happens consistently or intermittently
- Any workarounds you've found
- Related configuration or environment details
-->

---

**Before submitting:**
- [ ] I have searched existing issues to ensure this bug hasn't been reported
- [ ] I have included the complete output of `skaha version --debug`
- [ ] I have provided clear steps to reproduce the issue
- [ ] I have included relevant error messages or logs

For more information on creating effective bug reports, see our [Bug Reporting Guide](https://shinybrar.github.io/skaha/bug-reports/).
