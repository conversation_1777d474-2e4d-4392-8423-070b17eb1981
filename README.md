# Skaha

[![Continous Integration](https://github.com/shinybrar/skaha/actions/workflows/ci.yml/badge.svg)](https://github.com/shinybrar/skaha/actions/workflows/ci.yml)
[![Continous Deployment](https://github.com/shinybrar/skaha/actions/workflows/cd.yml/badge.svg)](https://github.com/shinybrar/skaha/actions/workflows/cd.yml)
[![codecov](https://codecov.io/gh/shinybrar/skaha/graph/badge.svg?token=T247EQ0S2M)](https://codecov.io/gh/shinybrar/skaha)
[![CodeQL](https://github.com/shinybrar/skaha/actions/workflows/codeql-analysis.yml/badge.svg)](https://github.com/shinybrar/skaha/actions/workflows/codeql-analysis.yml)
[![OpenSSF Scorecard](https://api.scorecard.dev/projects/github.com/shinybrar/skaha/badge)](https://scorecard.dev/viewer/?uri=github.com/shinybrar/skaha)

## For more information, [check out the official documentation](https://shinybrar.github.io/skaha/).

---
<p align="center">
  <a href="Some Love">
    <img src="https://forthebadge.com/images/badges/built-with-love.svg">
  </a>
</p>
