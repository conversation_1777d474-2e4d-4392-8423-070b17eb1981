# Configuration

!!! info "Overview"
    S<PERSON><PERSON> automatically loads configuration from YAML files, environment variables, and command-line arguments. When you create sessions or clients, configuration is handled seamlessly behind the scenes.

## How It Works

When you use Skaha components like `AsyncSession`, `Session`, or `SkahaClient`, they automatically load configuration in this priority order:

1. **Arguments passed to constructors** (highest priority)
2. **Environment variables** with `SKAHA_` prefix
3. **YAML configuration file** at `~/.skaha/config.yaml`
4. **Default values** (lowest priority)

```python
from skaha.session import AsyncSession

# Automatically loads configuration
session = AsyncSession()

# Override specific settings
session = AsyncSession(timeout=60, loglevel=10)
```

## Configuration File

### Location

The configuration file is automatically loaded from:

```
~/.skaha/config.yaml
```

### Example Configuration

```yaml title="~/.skaha/config.yaml"
# Server connection settings
timeout: 30
concurrency: 32
loglevel: 20  # INFO level

# Authentication configuration
auth:
  mode: "x509"  # or "oidc"
  x509:
    path: "~/.ssl/cadcproxy.pem"
    server:
      name: "CADC-CANFAR"
      url: "https://ws-uv.canfar.net/skaha"
  oidc:
    server:
      name: "CADC-CANFAR" 
      url: "https://ws-uv.canfar.net/skaha"
    endpoints:
      discovery: "https://oidc.example.com/.well-known/openid_configuration"
      token: "https://oidc.example.com/token"

# Container registry settings
registry:
  host: "images.canfar.net"
  username: "your-username"
  password: "your-password"
```

## Environment Variables

Override any configuration using environment variables with the `SKAHA_` prefix:

```bash
# Basic settings
export SKAHA_TIMEOUT=60
export SKAHA_LOGLEVEL=10
export SKAHA_CONCURRENCY=64

# Nested settings use double underscores
export SKAHA_AUTH__MODE=oidc
export SKAHA_AUTH__X509__PATH=/path/to/cert.pem
export SKAHA_REGISTRY__HOST=my-registry.com
```

## Configuration Management

### View Current Configuration

```bash
# Show current configuration
skaha config show
```

### Programmatic Access

```python
from skaha.models.config import Configuration

# Load configuration (automatic)
config = Configuration()

# Access settings
print(f"Timeout: {config.timeout}")
print(f"Auth mode: {config.auth.mode}")
print(f"Server URL: {config.url}")

# Save configuration
config.loglevel = 10
config.save()
```

## Configuration Options

### Core Settings

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `timeout` | `int` | `30` | HTTP request timeout (seconds) |
| `concurrency` | `int` | `32` | Max concurrent connections |
| `loglevel` | `int` | `20` | Logging level (10=DEBUG, 20=INFO, 30=WARNING, 40=ERROR, 50=CRITICAL) |

### Authentication Settings

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `auth.mode` | `str` | `"default"` | Authentication mode: `"x509"`, `"oidc"`, or `"default"` |
| `auth.x509.path` | `Path` | `None` | Path to X.509 certificate file |
| `auth.oidc.client.identity` | `str` | `None` | OIDC client ID |
| `auth.oidc.client.secret` | `str` | `None` | OIDC client secret |

### Registry Settings

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `registry.host` | `str` | `"images.canfar.net"` | Container registry hostname |
| `registry.username` | `str` | `None` | Registry username |
| `registry.password` | `str` | `None` | Registry password |

## Examples

### Basic Usage

```python
from skaha.session import AsyncSession

# Uses default configuration
async with AsyncSession() as session:
    sessions = await session.fetch()
```

### Custom Configuration

```python
from skaha.session import AsyncSession

# Override specific settings
async with AsyncSession(
    timeout=60,
    loglevel=10,  # DEBUG level
    concurrency=64
) as session:
    sessions = await session.fetch()
```

### Environment-Based Configuration

```bash
# Set environment variables
export SKAHA_TIMEOUT=60
export SKAHA_AUTH__MODE=oidc
export SKAHA_LOGLEVEL=10

# Python code uses environment settings
python -c "
from skaha.session import AsyncSession
import asyncio

async def main():
    async with AsyncSession() as session:
        print(f'Timeout: {session.timeout}')
        print(f'Auth mode: {session.auth.mode}')
        print(f'Log level: {session.loglevel}')

asyncio.run(main())
"
```

## Troubleshooting

### Configuration Not Loading

!!! warning "Common Issues"
    - **File not found**: Ensure `~/.skaha/config.yaml` exists and is readable
    - **Invalid YAML**: Check YAML syntax with a validator
    - **Permission errors**: Verify file permissions allow reading

### Debug Configuration Loading

```python
from skaha.models.config import Configuration
import logging

# Enable debug logging
logging.basicConfig(level=logging.DEBUG)

# Load configuration with debug output
config = Configuration()
```

### Reset to Defaults

```bash
# Remove configuration file to reset to defaults
rm ~/.skaha/config.yaml

# Or backup and recreate
mv ~/.skaha/config.yaml ~/.skaha/config.yaml.backup
```

!!! tip "Best Practices"
    - Use environment variables for sensitive data like passwords
    - Keep configuration files in version control (without secrets)
    - Use different configuration files for different environments
    - Test configuration changes with `skaha config show`
