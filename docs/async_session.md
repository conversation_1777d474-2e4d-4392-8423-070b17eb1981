# Asynchronous Sessions

!!! info "Overview"
    Skaha now supports asynchronous sessions using the `AsyncSession` class while maintaining 1-to-1 compatibility with the `Session` class.

::: skaha.session.AsyncSession
    handler: python
    selection:
      members:
        - fetch
        - create
        - info
        - logs
        - destroy
    rendering:
      members_order: source
      show_root_heading: true
      show_source: true
      heading_level: 2
