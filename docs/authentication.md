# Authentication System

Skaha provides comprehensive support for multiple authentication methods with automatic credential management, expiry tracking, and seamless refresh capabilities for OIDC tokens.

## Overview

Skaha supports the following:
- **Authentication Methods**: X.509 Certificates, OIDC Tokens, and Bearer Tokens
- **Automatic Refresh**: Seamless credential renewal without user intervention for OIDC tokens
- **Expiry Tracking**: Monitoring for OIDC tokens and X.509 certificates
- **Priority-based Selection**: Intelligent authentication method selection
- **Storage & Persistence**: Storage of credentials and configuration in `~/.config/skaha/config.yaml`

## Authentication Modes

### X.509 Certificate Authentication

X.509 certificates provide secure, long-term authentication for CANFAR services.

#### Default Mode

The default authentication mode is X.509 certificates.

!!! info "Default Certificate Location"
    The default certificate is automatically looked up at `$HOME/.ssl/cadcproxy.pem`.

```python
from skaha.session import AsyncSession

# Uses default certificate at ~/.ssl/cadcproxy.pem
session = AsyncSession()
```

#### Custom X.509 Certificate Location

For custom certificate locations, configure the X.509 authentication mode:

```python
from pathlib import Path
from skaha.session import AsyncSession

# Configure custom certificate path
session = AsyncSession(certificate=Path("/custom/path/to/cert.pem"))
```

#### Certificate Generation

Generate a new certificate using the CLI:

```bash
# Interactive certificate generation
skaha auth login
```

### OIDC Token Authentication

OpenID Connect (OIDC) provides modern, token-based authentication with automatic refresh capabilities.

#### Configuration

```python
from skaha.client import SkahaClient

# Configure OIDC authentication
client = SkahaClient()
client.auth.mode = "oidc"

# OIDC configuration is typically set via CLI
# skaha auth login
```

#### Token Refresh

OIDC tokens are automatically refreshed when they expire:

!!! success "Automatic Token Refresh"
    The authentication system automatically detects expired access tokens and uses refresh tokens to obtain new credentials without user intervention.

### Bearer Token Authentication

For direct token usage, use the `token` parameter:

```python
from pydantic import SecretStr
from skaha.session import AsyncSession

token = SecretStr("your-bearer-token-here")
# Use bearer token directly
session = AsyncSession(token=token)
```

!!! warning "Token Security"
    Always use `SecretStr` for tokens to prevent accidental logging of sensitive credentials.
    ```python
    token = SecretStr("your-bearer-token-here")
    print(token)  # This will not print the actual token
    >>> *******
    ```

## Authentication Priority

The authentication system follows a strict priority order:

1. **User-provided credentials (token, certificate)** (highest priority)
2. **Configured authentication via `skaha auth login`** (oidc, x509, default)
3. **Default certificate** (fallback)

## Automatic Authentication Refresh

The authentication system includes automatic refresh capabilities that work transparently in the background.

### How It Works

1. **Expiry Detection**: Before each HTTP request, the system checks if credentials are expired
2. **Automatic Refresh**: If expired, and if credentials can be auto-refreshed, they are.
3. **Request Continuation**: The original request proceeds with fresh credentials
4. **Configuration Persistence**: Updated credentials are automatically saved to disk

### Refresh Behavior by Mode

#### OIDC Token Refresh

```python
# When OIDC access tokens expire, the system automatically:
# 1. Detects access token expiry
# 2. Uses the refresh token to obtain a new access token
# 3. Updates token expiry information
# 4. Saves the new configuration
# 5. Continues with the request
```

### Refresh Token Expiry

If the OIDC refresh token itself is expired, the system will raise an `AuthenticationError`:

```python
from skaha.hooks.httpx.auth import AuthenticationError

try:
    response = client.client.get("/session")
except AuthenticationError as e:
    print(f"Authentication failed: {e}")
    # User needs to re-authenticate via CLI
    # skaha auth login
```

## Checking Authentication Status

### Current Authentication Mode

```python
from skaha.client import SkahaClient

client = SkahaClient()
print(f"Authentication mode: {client.auth.mode}")
```

### Expiry Information

```python
import time
from skaha.client import SkahaClient

client = SkahaClient()

if client.expiry:
    time_left = client.expiry - time.time()
    if time_left > 0:
        print(f"Authentication expires in {time_left:.0f} seconds")
    else:
        print("Authentication is expired")
else:
    print("No expiry tracking (user-provided credentials)")
```

### Validation

```python
from skaha.client import SkahaClient

client = SkahaClient()

# Check if current authentication is valid
if client.auth.valid():
    print("Authentication configuration is valid")
else:
    print("Authentication configuration is invalid")

# Check if authentication is expired
if client.auth.expired():
    print("Authentication is expired")
else:
    print("Authentication is active")
```

## Configuration Management

### Viewing Configuration

```bash
# View current authentication configuration
skaha config show
```

### Clearing Authentication

```bash
# Clear all authentication credentials
skaha auth logout
```

### Manual Configuration

Authentication can be configured programmatically:

```python
from skaha.models.config import Configuration

# Load and modify configuration
config = Configuration()
config.auth.mode = "oidc"
config.save()
```

## Troubleshooting

### Common Issues

#### Certificate Not Found

```
FileNotFoundError: cert file /path/to/cert.pem does not exist
```

**Solution**: Generate a new certificate or update the path:

```bash
skaha auth login
```

#### OIDC Refresh Failed

```
AuthenticationError: Refresh token invalid, full re-authentication required
```

**Solution**: Re-authenticate via CLI:

```bash
skaha auth login
```

#### Permission Denied

```
PermissionError: cert file /path/to/cert.pem is not readable
```

**Solution**: Fix file permissions:

```bash
chmod 600 /path/to/cert.pem
```

### Debug Logging

Enable debug logging to troubleshoot authentication issues:

```python
import logging
from skaha.client import SkahaClient

# Enable debug logging
client = SkahaClient(loglevel=logging.DEBUG)

# This will log:
# - Authentication mode selection
# - Expiry checking
# - Refresh attempts
# - Configuration updates
```

## Security Considerations

!!! danger "Security Best Practices"
    - Never commit certificates or tokens to version control
    - Use `SecretStr` for sensitive credentials in code
    - Regularly rotate certificates and tokens
    - Monitor authentication logs for suspicious activity
    - Use the most restrictive authentication method for your use case

### Credential Storage

- **Certificates**: Stored as PEM files with 600 permissions
- **Configuration**: Stored in YAML format at `~/.config/skaha/config.yaml`
- **Tokens**: Stored encrypted in the configuration file

### Network Security

- All communications use HTTPS/TLS encryption
- Certificate-based authentication provides mutual TLS
- OIDC tokens are transmitted securely via Authorization headers

## Technical Implementation

### HTTPx Authentication Hooks

The automatic authentication refresh is implemented using HTTPx event hooks that intercept requests before they are sent:

```python
# The authentication system uses HTTPx request hooks
# These hooks are automatically configured when creating SkahaClient instances

from skaha.client import SkahaClient

client = SkahaClient()
# Authentication hooks are automatically attached to both
# client.client (sync) and client.asynclient (async)
```

#### Hook Behavior

1. **Request Interception**: Every outgoing HTTP request is intercepted
2. **Expiry Check**: The hook checks if current credentials are expired
3. **Conditional Refresh**: If expired, appropriate refresh logic is executed
4. **Header Update**: Request headers are updated with fresh credentials
5. **Request Continuation**: The request proceeds with valid authentication

#### Bypass Conditions

The authentication hooks automatically bypass refresh in these scenarios:

- **User-provided tokens**: When `SkahaClient(token=...)` is used
- **User-provided certificates**: When `SkahaClient(certificate=...)` is used
- **Valid credentials**: When current credentials are not expired

### Error Handling

The authentication system provides comprehensive error handling:

#### AuthenticationError

Raised when authentication refresh fails:

```python
from skaha.hooks.httpx.auth import AuthenticationError

try:
    response = client.client.get("/session")
except AuthenticationError as e:
    # Handle authentication failure
    print(f"Authentication failed: {e}")
    # Typically requires user re-authentication
```

#### Common Error Scenarios

1. **Expired Refresh Token**: OIDC refresh token is no longer valid
2. **Invalid Configuration**: Missing required authentication parameters
3. **Network Errors**: Unable to reach authentication endpoints
4. **Certificate Issues**: Certificate file not found or corrupted

### Performance Considerations

#### Caching

- **Expiry Checks**: Minimal overhead, uses cached expiry timestamps
- **Hook Execution**: Only executes refresh logic when credentials are expired
- **Configuration Updates**: Batched writes to minimize I/O operations

#### Concurrency

- **Thread Safety**: Authentication hooks are thread-safe for concurrent requests
- **Async Support**: Full support for asyncio-based applications
- **Connection Pooling**: Maintains separate connection pools for different auth modes

## Advanced Usage

### Custom Authentication Flows

For advanced use cases, you can implement custom authentication:

```python
from skaha.auth import oidc, x509
from skaha.models.auth import OIDC, X509

# Custom OIDC authentication
oidc_config = OIDC()
oidc_config.endpoints.token = "https://custom-oidc.example.com/token"
oidc_config.client.identity = "custom-client-id"
# ... configure other parameters

# Custom X.509 authentication
x509_config = X509()
x509_config.path = Path("/custom/cert.pem")
# ... configure other parameters
```

### Programmatic Token Refresh

Manually refresh tokens when needed:

```python
import asyncio
from skaha.auth.oidc import refresh_token

async def manual_refresh():
    tokens = await refresh_token(
        token_url="https://oidc.example.com/token",
        client_id="<IDENTITY>",
        client_secret="<SECRET>",
        refresh_token="<TOKEN>",
    )
    return tokens["access_token"]

# Use in async context
new_token = asyncio.run(manual_refresh())
```

### Configuration Validation

Validate authentication configuration before use:

```python
from skaha.models.config import Configuration

config = Configuration()

# Validate specific authentication modes
if config.auth.mode == "oidc":
    if config.auth.oidc.valid():
        print("OIDC configuration is valid")
    else:
        print("OIDC configuration is missing required fields")

if config.auth.mode == "x509":
    if config.auth.x509.valid():
        print("X.509 configuration is valid")
    else:
        print("X.509 configuration is invalid")
```

## API Reference

### Authentication Models

- [`Authentication`](../api/models/auth/#authentication): Main authentication configuration
- [`OIDC`](../api/models/auth/#oidc): OIDC-specific configuration
- [`X509`](../api/models/auth/#x509): X.509 certificate configuration

### Authentication Functions

- [`oidc.refresh_token()`](../api/auth/oidc/#refresh_token): Manual OIDC token refresh
- [`x509.authenticate()`](../api/auth/x509/#authenticate): X.509 certificate authentication
- [`create_auth_hook()`](../api/hooks/httpx/auth/#create_auth_hook): Create HTTPx authentication hooks

### Configuration Management

- [`Configuration()`](../api/models/config/): Automatically loads configuration from file
- [`Configuration.save()`](../api/models/config/#save): Save configuration to file
